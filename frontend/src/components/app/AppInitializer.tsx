/**
 * 应用初始化组件
 * 
 * 解决权限状态恢复时序问题，确保应用完全初始化后再渲染内容
 */

import React, { useEffect, useState } from 'react'
import { useAuthStore } from '../../stores/auth'
import { tokenManager } from '../../utils/tokenManager'
import { STORAGE_KEYS } from '../../constants'

interface AppInitializerProps {
  children: React.ReactNode
}

interface InitializationState {
  isInitialized: boolean
  isLoading: boolean
  error: string | null
}

export const AppInitializer: React.FC<AppInitializerProps> = ({ children }) => {
  const [initState, setInitState] = useState<InitializationState>({
    isInitialized: false,
    isLoading: true,
    error: null
  })

  const {
    setAuthState,
    checkAuth
  } = useAuthStore()

  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      console.log('🚀 开始应用初始化...')
      setInitState(prev => ({ ...prev, isLoading: true, error: null }))

      // 1. 检查token是否存在
      const token = tokenManager.getToken()
      console.log('🔑 Token检查:', { hasToken: !!token })

      if (!token) {
        console.log('🔑 没有token，跳过权限恢复')
        setInitState({
          isInitialized: true,
          isLoading: false,
          error: null
        })
        return
      }

      // 2. 检查token是否有效
      const isTokenValid = tokenManager.isTokenValid()
      console.log('🔑 Token有效性检查:', { isValid: isTokenValid })

      if (!isTokenValid) {
        console.log('🔑 Token已过期，清除认证状态')
        tokenManager.clearToken()
        setInitState({
          isInitialized: true,
          isLoading: false,
          error: null
        })
        return
      }

      // 3. 从localStorage恢复权限状态
      await restoreAuthStateFromStorage()

      // 4. 验证服务器端认证状态
      await validateServerAuth()

      console.log('✅ 应用初始化完成')
      setInitState({
        isInitialized: true,
        isLoading: false,
        error: null
      })

    } catch (error) {
      console.error('❌ 应用初始化失败:', error)
      
      // 初始化失败，清除可能损坏的状态
      tokenManager.clearToken()
      
      setInitState({
        isInitialized: true,
        isLoading: false,
        error: error instanceof Error ? error.message : '应用初始化失败'
      })
    }
  }

  /**
   * 从localStorage恢复权限状态
   */
  const restoreAuthStateFromStorage = async () => {
    try {
      console.log('📦 从localStorage恢复权限状态...')

      const userInfoStr = localStorage.getItem(STORAGE_KEYS.USER_INFO)
      const permissionsStr = localStorage.getItem(STORAGE_KEYS.PERMISSIONS)
      const rolesStr = localStorage.getItem(STORAGE_KEYS.ROLES)
      const menusStr = localStorage.getItem(STORAGE_KEYS.MENUS)

      if (!userInfoStr || !permissionsStr || !rolesStr) {
        throw new Error('权限状态数据不完整')
      }

      const user = JSON.parse(userInfoStr)
      const permissions = JSON.parse(permissionsStr)
      const roles = JSON.parse(rolesStr)
      const menus = menusStr ? JSON.parse(menusStr) : []

      console.log('📦 权限状态恢复成功:', {
        user: user?.username,
        permissionsCount: permissions?.length || 0,
        rolesCount: roles?.length || 0,
        menusCount: menus?.length || 0
      })

      // 恢复到store
      setAuthState({
        isAuthenticated: true,
        user,
        token: tokenManager.getToken(),
        permissions,
        roles,
        menus,
        loading: false,
        error: null
      })

    } catch (error) {
      console.error('📦 权限状态恢复失败:', error)
      throw new Error('权限状态恢复失败')
    }
  }

  /**
   * 验证服务器端认证状态
   */
  const validateServerAuth = async () => {
    try {
      console.log('🔍 验证服务器端认证状态...')
      
      // 调用checkAuth验证服务器端状态
      await checkAuth()
      
      console.log('🔍 服务器端认证状态验证成功')
    } catch (error) {
      console.error('🔍 服务器端认证状态验证失败:', error)
      
      // 服务器验证失败，可能token已过期
      tokenManager.clearToken()
      throw new Error('服务器端认证验证失败')
    }
  }

  // 渲染加载状态
  if (initState.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">正在初始化应用...</p>
        </div>
      </div>
    )
  }

  // 渲染错误状态
  if (initState.error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="text-6xl">⚠️</div>
          <h2 className="text-2xl font-bold">应用初始化失败</h2>
          <p className="text-muted-foreground">{initState.error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  // 应用已初始化，渲染子组件
  return <>{children}</>
}

/**
 * 权限状态恢复Hook
 * 
 * 用于在组件中等待权限状态完全恢复
 */
export const useAuthStateReady = () => {
  const [isReady, setIsReady] = useState(false)
  const { isAuthenticated, permissions, user } = useAuthStore()

  useEffect(() => {
    // 检查权限状态是否已经恢复
    const checkAuthReady = () => {
      if (isAuthenticated && user && permissions.length >= 0) {
        setIsReady(true)
      } else if (!isAuthenticated && !tokenManager.getToken()) {
        // 未登录状态也算ready
        setIsReady(true)
      }
    }

    checkAuthReady()
  }, [isAuthenticated, permissions, user])

  return isReady
}
