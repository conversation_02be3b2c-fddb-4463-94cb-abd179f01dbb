/**
 * 应用守卫组件
 * 
 * 确保权限状态完全加载后再进行路由和权限检查
 */

import React from 'react'
import { useAuthStateReady } from './AppInitializer'
import { useAuthStore } from '../../stores/auth'

interface AppGuardProps {
  children: React.ReactNode
}

export const AppGuard: React.FC<AppGuardProps> = ({ children }) => {
  const isAuthReady = useAuthStateReady()

  // 权限状态还未准备好
  if (!isAuthReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">正在加载权限状态...</p>
        </div>
      </div>
    )
  }

  // 权限状态已准备好，渲染子组件
  return <>{children}</>
}

/**
 * 权限检查守卫Hook
 * 
 * 在权限状态完全恢复后进行权限检查
 */
export const usePermissionGuard = () => {
  const isAuthReady = useAuthStateReady()
  const { isAuthenticated, permissions } = useAuthStore()

  const checkPermission = (permission: string): boolean => {
    if (!isAuthReady) {
      console.log('🛡️ 权限状态未准备好，暂时拒绝访问')
      return false
    }

    if (!isAuthenticated) {
      console.log('🛡️ 用户未登录')
      return false
    }

    // 检查是否为超级管理员
    if (permissions.includes('*:*:*')) {
      console.log('🛡️ 超级管理员，允许访问')
      return true
    }

    // 检查具体权限
    const hasPermission = permissions.includes(permission)
    console.log('🛡️ 权限检查结果:', { permission, hasPermission })
    
    return hasPermission
  }

  const checkAnyPermission = (permissionList: string[]): boolean => {
    if (!isAuthReady) {
      return false
    }

    return permissionList.some(permission => checkPermission(permission))
  }

  const checkAllPermissions = (permissionList: string[]): boolean => {
    if (!isAuthReady) {
      return false
    }

    return permissionList.every(permission => checkPermission(permission))
  }

  return {
    isReady: isAuthReady,
    checkPermission,
    checkAnyPermission,
    checkAllPermissions
  }
}
