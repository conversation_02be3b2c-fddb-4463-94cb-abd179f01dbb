/**
 * 主播统计组件
 * 
 * 显示选中主播的详细统计数据，包括：
 * - 基础统计数据（充值、消费、用户数等）
 * - 首充统计数据（首充率、首充金额等）
 * - 支持时间范围筛选
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle, Button } from '@/components/ui'
import { RefreshCw, TrendingUp, Users, DollarSign, Target } from 'lucide-react'
import { DateRangePicker } from '@/components/ui'
import { useAnchorStats, useStatsFormatter } from '../hooks/useAnchorStats'
import { OperationsUtils } from '@/services/operations'
import type { AnchorListResponse } from '../types/operations'
import { addDays, startOfDay, endOfDay } from 'date-fns'
import type { DateRange } from 'react-day-picker'

export interface AnchorStatsProps {
  /** 选中的主播 */
  anchor: AnchorListResponse | null
  /** 时间范围 */
  dateRange?: DateRange
  /** 时间范围变化回调 */
  onDateRangeChange?: (range: DateRange | undefined) => void
  /** 返回回调 */
  onBack?: () => void
  /** 类名 */
  className?: string
}

/**
 * 统计卡片组件
 */
const StatsCard: React.FC<{
  title: string
  value: string
  description: string
  icon: React.ReactNode
  trend?: {
    value: number
    label: string
  }
  highlight?: boolean
  loading?: boolean
}> = ({ title, value, description, icon, trend, highlight, loading }) => {
  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${highlight ? 'border-primary bg-primary/5' : ''}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? (
            <div className="h-8 w-20 bg-muted animate-pulse rounded" />
          ) : (
            value
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
        {trend && !loading && (
          <p className="text-xs text-green-600 mt-1">
            <TrendingUp className="h-3 w-3 inline mr-1" />
            {trend.label}: {trend.value.toLocaleString()}
          </p>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 主播统计组件
 */
export const AnchorStats: React.FC<AnchorStatsProps> = ({
  anchor,
  dateRange,
  onDateRangeChange,
  className
}) => {
  const {
    statsData,
    firstRechargeData,
    statsLoading,
    firstRechargeLoading,
    error,
    loadAllStats,
    refreshStats
  } = useAnchorStats()
  
  const { formatStatsCards, formatFirstRechargeCards } = useStatsFormatter()
  
  // 处理时间范围变化
  const handleDateRangeChange = (range: DateRange | undefined) => {
    onDateRangeChange?.(range)
    
    if (anchor && range?.from && range?.to) {
      const startTime = OperationsUtils.dateToTimestamp(range.from)
      const endTime = OperationsUtils.dateToTimestamp(range.to)
      loadAllStats(anchor.id, startTime, endTime)
    }
  }
  
  // 处理刷新
  const handleRefresh = () => {
    refreshStats()
  }
  
  // 当主播变化时加载数据
  React.useEffect(() => {
    if (anchor) {
      // 总是使用时间范围，如果没有设置则使用默认的30天范围
      const effectiveDateRange = dateRange || {
        from: startOfDay(addDays(new Date(), -29)),
        to: endOfDay(new Date()),
      }

      if (effectiveDateRange.from && effectiveDateRange.to) {
        const startTime = OperationsUtils.dateToTimestamp(effectiveDateRange.from)
        const endTime = OperationsUtils.dateToTimestamp(effectiveDateRange.to)
        loadAllStats(anchor.id, startTime, endTime)
      }
    }
  }, [anchor, loadAllStats])
  
  if (!anchor) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        请选择一个主播查看统计数据
      </div>
    )
  }
  
  const statsCards = formatStatsCards(statsData)
  const firstRechargeCards = formatFirstRechargeCards(firstRechargeData)
  
  const loading = statsLoading || firstRechargeLoading
  
  return (
    <div className={className}>
      {/* 头部信息 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">主播统计</h2>
          <p className="text-muted-foreground">
            {anchor.nickname} ({anchor.username})
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* 时间范围选择器 */}
          <DateRangePicker
            onDateChange={handleDateRangeChange}
            className="w-[300px]"
          />

          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      )}
      
      {/* 基础统计数据 */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <DollarSign className="h-5 w-5 mr-2" />
          基础统计
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statsCards.map((card, index) => (
            <StatsCard
              key={index}
              title={card.title}
              value={card.value}
              description={card.description}
              icon={<DollarSign className="h-4 w-4" />}
              trend={card.trend}
              highlight={card.highlight}
              loading={loading}
            />
          ))}
        </div>
      </div>
      
      {/* 首充统计数据 */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Target className="h-5 w-5 mr-2" />
          首充统计
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {firstRechargeCards.map((card, index) => (
            <StatsCard
              key={index}
              title={card.title}
              value={card.value}
              description={card.description}
              icon={<Users className="h-4 w-4" />}
              trend={card.trend}
              loading={loading}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
