/**
 * 用户角色分配对话框组件
 * 
 * 专门用于分配用户角色，不包含用户信息编辑功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Checkbox
} from '@/components/ui'
import { Loader2, Shield, Users } from 'lucide-react'
import { RoleService } from '@/services/role'
import { UserService } from '@/services'
import type { User } from '@/types/user'
import type { Role } from '@/types'

export interface UserRoleAssignDialogProps {
  /** 是否打开 */
  open: boolean
  /** 关闭回调 */
  onOpenChange: (open: boolean) => void
  /** 用户信息 */
  user?: User | null
  /** 分配成功回调 */
  onSuccess: () => void
}

/**
 * 用户角色分配对话框组件
 */
const UserRoleAssignDialog: React.FC<UserRoleAssignDialogProps> = ({
  open,
  onOpenChange,
  user,
  onSuccess
}) => {
  const [saving, setSaving] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  
  // 角色相关状态
  const [roles, setRoles] = useState<Role[]>([])
  const [rolesLoading, setRolesLoading] = useState(false)
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([])

  // 加载角色列表
  const loadRoles = async () => {
    try {
      setRolesLoading(true)
      console.log('🔄 开始加载角色列表...')
      const result = await RoleService.pageRoles({
        pageNum: 1,
        pageSize: 100
      })
      console.log('✅ 角色列表加载成功，数量:', result.records?.length || 0)
      setRoles(result.records || [])
    } catch (error) {
      console.error('❌ 加载角色列表失败:', error)
      setSubmitError('加载角色列表失败: ' + (error as Error).message)
    } finally {
      setRolesLoading(false)
    }
  }

  // 加载用户角色
  const loadUserRoles = async (userId: number) => {
    try {
      console.log('🔄 开始加载用户角色:', userId)
      const roleIds = await UserService.getUserRoles(userId)
      console.log('✅ 用户角色加载成功:', roleIds)
      setSelectedRoleIds(roleIds)
    } catch (error) {
      console.error('❌ 加载用户角色失败:', error)
      setSubmitError('加载用户角色失败: ' + (error as Error).message)
    }
  }

  // 初始化数据
  useEffect(() => {
    if (open && user) {
      setSubmitError(null)
      setSelectedRoleIds([])
      loadRoles()
      loadUserRoles(user.id)
    }
  }, [open, user])

  // 处理角色选择
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedRoleIds([...selectedRoleIds, roleId])
    } else {
      setSelectedRoleIds(selectedRoleIds.filter(id => id !== roleId))
    }
  }

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRoleIds(roles.map(role => role.id))
    } else {
      setSelectedRoleIds([])
    }
  }

  // 提交角色分配
  const handleSubmit = async () => {
    if (!user) return

    try {
      setSaving(true)
      setSubmitError(null)

      console.log('🔄 开始分配角色:', { userId: user.id, roleIds: selectedRoleIds })
      await UserService.assignRoles(user.id, selectedRoleIds)
      console.log('✅ 角色分配成功')

      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      console.error('❌ 角色分配失败:', error)
      setSubmitError(error.message || '角色分配失败')
    } finally {
      setSaving(false)
    }
  }

  // 处理关闭
  const handleClose = () => {
    if (saving) return
    onOpenChange(false)
  }

  const isAllSelected = roles.length > 0 && selectedRoleIds.length === roles.length
  const isIndeterminate = selectedRoleIds.length > 0 && selectedRoleIds.length < roles.length

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            分配角色
          </DialogTitle>
          <DialogDescription>
            为用户 <span className="font-semibold text-foreground">"{user?.username}"</span> 分配系统角色
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {submitError}
          </div>
        )}

        <div className="space-y-4">
          {/* 用户信息展示 */}
          <div className="bg-muted/50 p-3 rounded-md">
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-2 text-muted-foreground" />
              <div className="flex-1">
                <div className="font-medium">{user?.realName || user?.username}</div>
                <div className="text-sm text-muted-foreground">
                  {user?.username} • {user?.email || '未设置邮箱'}
                </div>
              </div>
            </div>
          </div>

          {/* 角色选择 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">选择角色</h3>
              {rolesLoading && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  加载中...
                </div>
              )}
            </div>

            {!rolesLoading && roles.length > 0 && (
              <>
                {/* 全选选项 */}
                <div className="flex items-center space-x-2 pb-2 border-b">
                  <Checkbox
                    id="select-all"
                    checked={isAllSelected}
                    indeterminate={isIndeterminate}
                    onCheckedChange={handleSelectAll}
                  />
                  <label
                    htmlFor="select-all"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    全选 ({selectedRoleIds.length}/{roles.length})
                  </label>
                </div>

                {/* 角色列表 */}
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {roles.map((role) => (
                    <div key={role.id} className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded">
                      <Checkbox
                        id={`role-${role.id}`}
                        checked={selectedRoleIds.includes(role.id)}
                        onCheckedChange={(checked) => handleRoleChange(role.id, checked as boolean)}
                      />
                      <div className="flex-1 cursor-pointer" onClick={() => handleRoleChange(role.id, !selectedRoleIds.includes(role.id))}>
                        <label
                          htmlFor={`role-${role.id}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        >
                          {role.roleName}
                        </label>
                        {role.remark && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {role.remark}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}

            {!rolesLoading && roles.length === 0 && (
              <div className="text-center text-muted-foreground py-8 border rounded-md">
                <div className="text-lg mb-2">⚠️</div>
                <div>暂无可分配的角色</div>
                <div className="text-xs mt-1">请先创建角色</div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={saving}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={saving || rolesLoading}
          >
            {saving && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            确认分配
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default UserRoleAssignDialog
