/**
 * 重置密码对话框组件
 * 
 * 用于管理员重置用户密码，支持自定义密码输入和密码强度验证
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui'
import { Loader2, Eye, EyeOff, Key, CheckCircle, XCircle } from 'lucide-react'
import { useForm } from '@/hooks/useForm'
import type { User } from '@/types/user'

export interface ResetPasswordDialogProps {
  /** 是否打开 */
  open: boolean
  /** 关闭回调 */
  onOpenChange: (open: boolean) => void
  /** 用户信息 */
  user?: User | null
  /** 重置成功回调 */
  onSuccess: (newPassword: string) => void
}

interface ResetPasswordFormData {
  newPassword: string
  confirmPassword: string
}

/**
 * 密码强度检查
 */
const checkPasswordStrength = (password: string) => {
  const checks = {
    length: password.length >= 6,
    hasLetter: /[a-zA-Z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
  
  const score = Object.values(checks).filter(Boolean).length
  
  return {
    checks,
    score,
    level: score <= 1 ? 'weak' : score <= 2 ? 'medium' : score <= 3 ? 'strong' : 'very-strong'
  }
}

/**
 * 重置密码对话框组件
 */
const ResetPasswordDialog: React.FC<ResetPasswordDialogProps> = ({
  open,
  onOpenChange,
  user,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // 表单配置
  const form: any = useForm<ResetPasswordFormData>({
    defaultValues: {
      newPassword: '',
      confirmPassword: ''
    },
    fields: {
      newPassword: {
        rules: {
          required: '新密码不能为空',
          minLength: { value: 6, message: '密码至少6个字符' },
          maxLength: { value: 20, message: '密码不能超过20个字符' }
        }
      },
      confirmPassword: {
        rules: {
          required: '确认密码不能为空',
          validate: (value: string): string | boolean => {
            const newPassword: string = form.watch('newPassword')
            return value === newPassword || '两次输入的密码不一致'
          }
        }
      }
    }
  })

  const newPassword = form.watch('newPassword') || ''
  const confirmPassword = form.watch('confirmPassword') || ''
  const passwordStrength = checkPasswordStrength(newPassword)

  // 重置表单
  useEffect(() => {
    if (open) {
      setSubmitError(null)
      form.reset()
    }
  }, [open])

  // 提交表单
  const handleSubmit = async () => {
    if (!user) return

    // 手动验证表单
    let hasError = false

    // 验证新密码
    if (!newPassword) {
      form.setError('newPassword', '新密码不能为空')
      hasError = true
    } else if (newPassword.length < 6) {
      form.setError('newPassword', '密码至少6个字符')
      hasError = true
    } else if (newPassword.length > 20) {
      form.setError('newPassword', '密码不能超过20个字符')
      hasError = true
    }

    // 验证确认密码
    if (!confirmPassword) {
      form.setError('confirmPassword', '确认密码不能为空')
      hasError = true
    } else if (confirmPassword !== newPassword) {
      form.setError('confirmPassword', '两次输入的密码不一致')
      hasError = true
    }

    // 如果有错误，不提交
    if (hasError) return

    try {
      setLoading(true)
      setSubmitError(null)

      // 调用成功回调，传递新密码
      onSuccess(newPassword)
      onOpenChange(false)
    } catch (error: any) {
      setSubmitError(error.message || '重置密码失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取密码强度颜色
  const getStrengthColor = (level: string) => {
    switch (level) {
      case 'weak': return 'text-red-500'
      case 'medium': return 'text-yellow-500'
      case 'strong': return 'text-blue-500'
      case 'very-strong': return 'text-green-500'
      default: return 'text-gray-400'
    }
  }

  // 获取密码强度文本
  const getStrengthText = (level: string) => {
    switch (level) {
      case 'weak': return '弱'
      case 'medium': return '中等'
      case 'strong': return '强'
      case 'very-strong': return '很强'
      default: return ''
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Key className="w-5 h-5 mr-2" />
            重置密码
          </DialogTitle>
          <DialogDescription>
            为用户 <span className="font-semibold text-foreground">"{user?.username}"</span> 设置新密码
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {submitError}
          </div>
        )}

        <div className="space-y-4">
          {/* 新密码 */}
          <FormField>
            <FormItem>
              <FormLabel>新密码 *</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入新密码"
                    value={newPassword}
                    onChange={(e) => {
                      form.setValue('newPassword', e.target.value)
                      // 清除错误状态
                      if (form.formState.errors.newPassword) {
                        form.clearErrors('newPassword')
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage>{form.formState.errors.newPassword}</FormMessage>
            </FormItem>
          </FormField>

          {/* 密码强度指示器 */}
          {newPassword && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">密码强度:</span>
                <span className={`text-sm font-medium ${getStrengthColor(passwordStrength.level)}`}>
                  {getStrengthText(passwordStrength.level)}
                </span>
              </div>
              
              {/* 强度条 */}
              <div className="flex space-x-1">
                {[1, 2, 3, 4].map((level) => (
                  <div
                    key={level}
                    className={`h-2 flex-1 rounded-full ${
                      level <= passwordStrength.score
                        ? passwordStrength.level === 'weak' ? 'bg-red-500'
                        : passwordStrength.level === 'medium' ? 'bg-yellow-500'
                        : passwordStrength.level === 'strong' ? 'bg-blue-500'
                        : 'bg-green-500'
                        : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>

              {/* 密码要求检查 */}
              <div className="space-y-1 text-xs">
                <div className={`flex items-center ${passwordStrength.checks.length ? 'text-green-600' : 'text-gray-400'}`}>
                  {passwordStrength.checks.length ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
                  至少6个字符
                </div>
                <div className={`flex items-center ${passwordStrength.checks.hasLetter ? 'text-green-600' : 'text-gray-400'}`}>
                  {passwordStrength.checks.hasLetter ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
                  包含字母
                </div>
                <div className={`flex items-center ${passwordStrength.checks.hasNumber ? 'text-green-600' : 'text-gray-400'}`}>
                  {passwordStrength.checks.hasNumber ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
                  包含数字
                </div>
                <div className={`flex items-center ${passwordStrength.checks.hasSpecial ? 'text-green-600' : 'text-gray-400'}`}>
                  {passwordStrength.checks.hasSpecial ? <CheckCircle className="w-3 h-3 mr-1" /> : <XCircle className="w-3 h-3 mr-1" />}
                  包含特殊字符
                </div>
              </div>
            </div>
          )}

          {/* 确认密码 */}
          <FormField>
            <FormItem>
              <FormLabel>确认密码 *</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="请再次输入新密码"
                    value={confirmPassword}
                    onChange={(e) => {
                      form.setValue('confirmPassword', e.target.value)
                      // 清除错误状态
                      if (form.formState.errors.confirmPassword) {
                        form.clearErrors('confirmPassword')
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage>{form.formState.errors.confirmPassword}</FormMessage>
            </FormItem>
          </FormField>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={loading || !newPassword || !confirmPassword || passwordStrength.score < 2}
          >
            {loading && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            重置密码
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ResetPasswordDialog
