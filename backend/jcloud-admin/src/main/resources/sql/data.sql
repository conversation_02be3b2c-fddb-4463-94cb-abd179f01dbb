-- jCloud权限管理系统初始化数据
-- 包含默认租户、管理员用户、角色、权限、菜单等基础数据

-- 1. 初始化默认租户
INSERT INTO `sys_tenant` (`id`, `tenant_code`, `tenant_name`, `contact_name`, `contact_phone`, `contact_email`, `status`, `max_user_count`, `remark`, `create_by`) VALUES
(1, 'DEFAULT', '默认租户', '系统管理员', '13800138000', '<EMAIL>', 1, 1000, '系统默认租户', 0);

-- 2. 初始化默认部门
INSERT INTO `sys_dept` (`id`, `tenant_id`, `parent_id`, `dept_code`, `dept_name`, `status`, `sort_order`, `remark`, `create_by`) VALUES
-- 根部门
(1, 1, 0, 'COMPANY', '总公司', 1, 1, '总公司', 0),

-- 一级部门
(2, 1, 1, 'TECH', '技术部', 1, 11, '技术研发部门', 0),
(3, 1, 1, 'MARKET', '市场部', 1, 12, '市场营销部门', 0),
(4, 1, 1, 'HR', '人事部', 1, 13, '人力资源部门', 0),
(5, 1, 1, 'FINANCE', '财务部', 1, 14, '财务管理部门', 0),

-- 技术部下属部门
(6, 1, 2, 'TECH_DEV', '开发组', 1, 111, '软件开发组', 0),
(7, 1, 2, 'TECH_TEST', '测试组', 1, 112, '软件测试组', 0),
(8, 1, 2, 'TECH_OPS', '运维组', 1, 113, '系统运维组', 0),

-- 开发组下属小组
(9, 1, 6, 'TECH_DEV_FE', '前端组', 1, 1111, '前端开发小组', 0),
(10, 1, 6, 'TECH_DEV_BE', '后端组', 1, 1112, '后端开发小组', 0),

-- 市场部下属部门
(11, 1, 3, 'MARKET_SALES', '销售组', 1, 121, '销售团队', 0),
(12, 1, 3, 'MARKET_PR', '推广组', 1, 122, '市场推广组', 0),

-- 人事部下属部门
(13, 1, 4, 'HR_RECRUIT', '招聘组', 1, 131, '人才招聘组', 0),
(14, 1, 4, 'HR_TRAIN', '培训组', 1, 132, '员工培训组', 0);

-- 3. 初始化系统用户
-- 密码为：123456（使用BCrypt加密）
INSERT INTO `sys_user` (`id`, `tenant_id`, `username`, `password`, `nickname`, `real_name`, `email`, `phone`, `status`, `is_admin`, `remark`, `create_by`) VALUES
(1, 1, 'admin', '$2a$10$7JB720yubVSOfvVWbfXCOOxjTOQcQmMlGNw6dpKq.jSQ/0/UrzKAm', '超级管理员', '系统管理员', '<EMAIL>', '13800138000', 1, 1, '系统超级管理员', 0),
(2, 1, 'test', '$2a$10$7JB720yubVSOfvVWbfXCOOxjTOQcQmMlGNw6dpKq.jSQ/0/UrzKAm', '测试用户', '测试用户', '<EMAIL>', '13800138001', 1, 0, '系统测试用户', 1);

-- 4. 初始化用户部门关联
INSERT INTO `sys_user_dept` (`tenant_id`, `user_id`, `dept_id`, `is_main`, `create_by`) VALUES
(1, 1, 2, 1, 0),
(1, 2, 3, 1, 1);

-- 5. 初始化系统角色
INSERT INTO `sys_role` (`id`, `tenant_id`, `role_code`, `role_name`, `role_type`, `data_scope`, `status`, `sort_order`, `remark`, `create_by`) VALUES
(1, 1, 'SUPER_ADMIN', '超级管理员', 'SYSTEM', 'ALL', 1, 1, '系统超级管理员角色，拥有所有权限', 0),
(2, 1, 'ADMIN', '管理员', 'SYSTEM', 'DEPT_AND_SUB', 1, 2, '系统管理员角色', 0),
(3, 1, 'USER', '普通用户', 'SYSTEM', 'SELF', 1, 3, '普通用户角色', 0),
(4, 1, 'ANCHOR', '主播角色', 'SYSTEM', 'SELF', 1, 4, '主播用户角色，只能查看自己的数据', 0),
(5, 1, 'AGENT', '代理角色', 'SYSTEM', 'DEPT_AND_SUB', 1, 5, '代理用户角色，可以查看部门及子部门数据', 0);

-- 6. 初始化用户角色关联
INSERT INTO `sys_user_role` (`tenant_id`, `user_id`, `role_id`, `create_by`) VALUES
(1, 1, 1, 0),
(1, 2, 3, 1);

-- 7. 初始化系统菜单
INSERT INTO `sys_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `path`, `component`, `icon`, `permission_code`, `status`, `visible`, `sort_order`, `remark`) VALUES
-- 一级菜单
(1, 0, '系统管理', 0, '/system', NULL, 'Settings', NULL, 1, 1, 1, '系统管理目录'),
(2, 0, '权限管理', 0, '/auth', NULL, 'Shield', NULL, 1, 1, 2, '权限管理目录'),
(3, 0, '监控管理', 0, '/monitor', NULL, 'Monitor', NULL, 1, 1, 3, '监控管理目录'),
(4, 0, '财务数据管理', 0, '/financial', NULL, 'TrendingUp', NULL, 1, 1, 4, '财务数据管理目录'),

-- 系统管理子菜单
(11, 1, '用户管理', 1, '/system/user', '/system/user/index', 'Users', 'system:user:list', 1, 1, 1, '用户管理菜单'),
(12, 1, '部门管理', 1, '/system/dept', '/system/dept/index', 'Building', 'system:dept:list', 1, 1, 2, '部门管理菜单'),
(13, 1, '租户管理', 1, '/system/tenant', '/system/tenant/index', 'Building2', 'system:tenant:list', 1, 1, 3, '租户管理菜单'),

-- 权限管理子菜单
(21, 2, '角色管理', 1, '/auth/role', '/auth/role/index', 'UserCheck', 'auth:role:list', 1, 1, 1, '角色管理菜单'),
(22, 2, '菜单管理', 1, '/auth/menu', '/auth/menu/index', 'Menu', 'auth:menu:list', 1, 1, 2, '菜单管理菜单'),
(23, 2, '权限管理', 1, '/auth/permission', '/auth/permission/index', 'Key', 'auth:permission:list', 1, 1, 3, '权限管理菜单'),

-- 监控管理子菜单
(31, 3, '操作日志', 1, '/monitor/operlog', '/monitor/operlog/index', 'FileText', 'monitor:operlog:list', 1, 1, 1, '操作日志菜单'),
(32, 3, '系统信息', 1, '/monitor/server', '/monitor/server/index', 'Server', 'monitor:server:list', 1, 1, 2, '系统信息菜单'),

-- 财务数据管理子菜单
(41, 4, '财务数据统计', 1, '/financial/dashboard', '/financial/dashboard/index', 'BarChart3', 'financial:stats:query', 1, 1, 1, '财务数据统计菜单'),

-- 用户管理按钮权限
(111, 11, '用户查询', 2, NULL, NULL, NULL, 'system:user:query', 1, 1, 1, '用户查询按钮'),
(112, 11, '用户新增', 2, NULL, NULL, NULL, 'system:user:add', 1, 1, 2, '用户新增按钮'),
(113, 11, '用户修改', 2, NULL, NULL, NULL, 'system:user:edit', 1, 1, 3, '用户修改按钮'),
(114, 11, '用户删除', 2, NULL, NULL, NULL, 'system:user:remove', 1, 1, 4, '用户删除按钮'),
(115, 11, '重置密码', 2, NULL, NULL, NULL, 'system:user:resetPwd', 1, 1, 5, '重置密码按钮'),

-- 部门管理按钮权限
(121, 12, '部门查询', 2, NULL, NULL, NULL, 'system:dept:query', 1, 1, 1, '部门查询按钮'),
(122, 12, '部门新增', 2, NULL, NULL, NULL, 'system:dept:add', 1, 1, 2, '部门新增按钮'),
(123, 12, '部门修改', 2, NULL, NULL, NULL, 'system:dept:edit', 1, 1, 3, '部门修改按钮'),
(124, 12, '部门删除', 2, NULL, NULL, NULL, 'system:dept:remove', 1, 1, 4, '部门删除按钮'),

-- 租户管理按钮权限
(131, 13, '租户查询', 2, NULL, NULL, NULL, 'system:tenant:query', 1, 1, 1, '租户查询按钮'),
(132, 13, '租户新增', 2, NULL, NULL, NULL, 'system:tenant:add', 1, 1, 2, '租户新增按钮'),
(133, 13, '租户修改', 2, NULL, NULL, NULL, 'system:tenant:edit', 1, 1, 3, '租户修改按钮'),
(134, 13, '租户删除', 2, NULL, NULL, NULL, 'system:tenant:remove', 1, 1, 4, '租户删除按钮'),

-- 角色管理按钮权限
(211, 21, '角色查询', 2, NULL, NULL, NULL, 'auth:role:query', 1, 1, 1, '角色查询按钮'),
(212, 21, '角色新增', 2, NULL, NULL, NULL, 'auth:role:add', 1, 1, 2, '角色新增按钮'),
(213, 21, '角色修改', 2, NULL, NULL, NULL, 'auth:role:edit', 1, 1, 3, '角色修改按钮'),
(214, 21, '角色删除', 2, NULL, NULL, NULL, 'auth:role:remove', 1, 1, 4, '角色删除按钮'),
(215, 21, '分配权限', 2, NULL, NULL, NULL, 'auth:role:authUser', 1, 1, 5, '分配权限按钮'),

-- 菜单管理按钮权限
(221, 22, '菜单查询', 2, NULL, NULL, NULL, 'auth:menu:query', 1, 1, 1, '菜单查询按钮'),
(222, 22, '菜单新增', 2, NULL, NULL, NULL, 'auth:menu:add', 1, 1, 2, '菜单新增按钮'),
(223, 22, '菜单修改', 2, NULL, NULL, NULL, 'auth:menu:edit', 1, 1, 3, '菜单修改按钮'),
(224, 22, '菜单删除', 2, NULL, NULL, NULL, 'auth:menu:remove', 1, 1, 4, '菜单删除按钮'),

-- 财务数据统计按钮权限
(411, 41, '财务数据查询', 2, NULL, NULL, NULL, 'financial:stats:query', 1, 1, 1, '财务数据查询按钮'),
(412, 41, '财务数据查看', 2, NULL, NULL, NULL, 'financial:stats:view', 1, 1, 2, '财务数据查看按钮'),
(413, 41, '财务数据导出', 2, NULL, NULL, NULL, 'financial:stats:export', 1, 1, 3, '财务数据导出按钮');

-- 8. 初始化系统权限
INSERT INTO `sys_permission` (`id`, `permission_code`, `permission_name`, `permission_type`, `resource_path`, `method`, `status`, `sort_order`, `remark`) VALUES
-- 用户管理权限
(1, 'system:user:list', '用户列表', 'API', '/api/system/user/list', 'GET', 1, 1, '用户列表查询权限'),
(2, 'system:user:query', '用户查询', 'API', '/api/system/user/*', 'GET', 1, 2, '用户详情查询权限'),
(3, 'system:user:add', '用户新增', 'API', '/api/system/user', 'POST', 1, 3, '用户新增权限'),
(4, 'system:user:edit', '用户修改', 'API', '/api/system/user/*', 'PUT', 1, 4, '用户修改权限'),
(5, 'system:user:remove', '用户删除', 'API', '/api/system/user/*', 'DELETE', 1, 5, '用户删除权限'),
(6, 'system:user:resetPwd', '重置密码', 'API', '/api/system/user/resetPwd', 'PUT', 1, 6, '重置密码权限'),

-- 部门管理权限
(11, 'system:dept:list', '部门列表', 'API', '/api/system/dept/list', 'GET', 1, 11, '部门列表查询权限'),
(12, 'system:dept:query', '部门查询', 'API', '/api/system/dept/*', 'GET', 1, 12, '部门详情查询权限'),
(13, 'system:dept:add', '部门新增', 'API', '/api/system/dept', 'POST', 1, 13, '部门新增权限'),
(14, 'system:dept:edit', '部门修改', 'API', '/api/system/dept/*', 'PUT', 1, 14, '部门修改权限'),
(15, 'system:dept:remove', '部门删除', 'API', '/api/system/dept/*', 'DELETE', 1, 15, '部门删除权限'),

-- 租户管理权限
(21, 'system:tenant:list', '租户列表', 'API', '/api/system/tenant/list', 'GET', 1, 21, '租户列表查询权限'),
(22, 'system:tenant:query', '租户查询', 'API', '/api/system/tenant/*', 'GET', 1, 22, '租户详情查询权限'),
(23, 'system:tenant:add', '租户新增', 'API', '/api/system/tenant', 'POST', 1, 23, '租户新增权限'),
(24, 'system:tenant:edit', '租户修改', 'API', '/api/system/tenant/*', 'PUT', 1, 24, '租户修改权限'),
(25, 'system:tenant:remove', '租户删除', 'API', '/api/system/tenant/*', 'DELETE', 1, 25, '租户删除权限'),

-- 角色管理权限
(31, 'auth:role:list', '角色列表', 'API', '/api/auth/role/list', 'GET', 1, 31, '角色列表查询权限'),
(32, 'auth:role:query', '角色查询', 'API', '/api/auth/role/*', 'GET', 1, 32, '角色详情查询权限'),
(33, 'auth:role:add', '角色新增', 'API', '/api/auth/role', 'POST', 1, 33, '角色新增权限'),
(34, 'auth:role:edit', '角色修改', 'API', '/api/auth/role/*', 'PUT', 1, 34, '角色修改权限'),
(35, 'auth:role:remove', '角色删除', 'API', '/api/auth/role/*', 'DELETE', 1, 35, '角色删除权限'),
(36, 'auth:role:authUser', '分配权限', 'API', '/api/auth/role/authUser', 'PUT', 1, 36, '角色分配权限'),

-- 菜单管理权限
(41, 'auth:menu:list', '菜单列表', 'API', '/api/auth/menu/list', 'GET', 1, 41, '菜单列表查询权限'),
(42, 'auth:menu:query', '菜单查询', 'API', '/api/auth/menu/*', 'GET', 1, 42, '菜单详情查询权限'),
(43, 'auth:menu:add', '菜单新增', 'API', '/api/auth/menu', 'POST', 1, 43, '菜单新增权限'),
(44, 'auth:menu:edit', '菜单修改', 'API', '/api/auth/menu/*', 'PUT', 1, 44, '菜单修改权限'),
(45, 'auth:menu:remove', '菜单删除', 'API', '/api/auth/menu/*', 'DELETE', 1, 45, '菜单删除权限'),

-- 权限管理权限
(51, 'auth:permission:list', '权限列表', 'API', '/api/auth/permission/list', 'GET', 1, 51, '权限列表查询权限'),
(52, 'auth:permission:query', '权限查询', 'API', '/api/auth/permission/*', 'GET', 1, 52, '权限详情查询权限'),
(53, 'auth:permission:add', '权限新增', 'API', '/api/auth/permission', 'POST', 1, 53, '权限新增权限'),
(54, 'auth:permission:edit', '权限修改', 'API', '/api/auth/permission/*', 'PUT', 1, 54, '权限修改权限'),
(55, 'auth:permission:remove', '权限删除', 'API', '/api/auth/permission/*', 'DELETE', 1, 55, '权限删除权限'),

-- 操作日志权限
(61, 'monitor:operlog:list', '操作日志列表', 'API', '/api/monitor/operlog/list', 'GET', 1, 61, '操作日志列表查询权限'),
(62, 'monitor:operlog:query', '操作日志查询', 'API', '/api/monitor/operlog/*', 'GET', 1, 62, '操作日志详情查询权限'),
(63, 'monitor:operlog:remove', '操作日志删除', 'API', '/api/monitor/operlog/*', 'DELETE', 1, 63, '操作日志删除权限'),

-- 系统信息权限
(71, 'monitor:server:list', '系统信息', 'API', '/api/monitor/server/info', 'GET', 1, 71, '系统信息查询权限'),

-- 财务数据管理权限
(81, 'financial:stats:query', '财务数据查询', 'API', '/api/financial/stats/query', 'POST', 1, 81, '财务数据查询权限'),
(82, 'financial:stats:view', '财务数据查看', 'API', '/api/financial/stats/today', 'GET', 1, 82, '财务数据查看权限'),
(83, 'financial:stats:export', '财务数据导出', 'API', '/api/financial/stats/export', 'POST', 1, 83, '财务数据导出权限');

-- 9. 初始化超级管理员角色权限关联（拥有所有权限）
INSERT INTO `sys_role_permission` (`tenant_id`, `role_id`, `permission_id`, `create_by`)
SELECT 1, 1, id, 0 FROM `sys_permission`;

-- 10. 初始化超级管理员角色菜单关联（拥有所有菜单）
INSERT INTO `sys_role_menu` (`tenant_id`, `role_id`, `menu_id`, `create_by`)
SELECT 1, 1, id, 0 FROM `sys_menu`;
