-- 创建GetSubordinateProfit存储过程
-- 递归查找指定用户的所有下级用户并计算统计数据

DELIMITER $$

DROP PROCEDURE IF EXISTS GetSubordinateProfit$$

CREATE PROCEDURE GetSubordinateProfit(
    IN p_root_uid INT,
    IN p_max_level INT,
    IN p_start_time VARCHAR(20),
    IN p_end_time VARCHAR(20)
)
BEGIN
    DECLARE v_start_timestamp BIGINT;
    DECLARE v_end_timestamp BIGINT;
    DECLARE v_total_users INT DEFAULT 0;
    DECLARE v_total_recharge DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_consume DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_profit DECIMAL(15,2) DEFAULT 0.00;
    
    -- 转换时间戳参数
    SET v_start_timestamp = CAST(p_start_time AS UNSIGNED);
    SET v_end_timestamp = CAST(p_end_time AS UNSIGNED);
    
    -- 创建临时表存储下级用户层级关系
    DROP TEMPORARY TABLE IF EXISTS temp_user_hierarchy;
    CREATE TEMPORARY TABLE temp_user_hierarchy (
        user_id INT,
        level_num INT,
        INDEX idx_user_id (user_id)
    );
    
    -- 递归查找所有下级用户
    -- 第一层：直接下级用户
    INSERT INTO temp_user_hierarchy (user_id, level_num)
    SELECT id, 1
    FROM vim_user 
    WHERE invite_user = p_root_uid
    AND id != p_root_uid;
    
    -- 递归查找更深层级的下级用户
    SET @current_level = 1;
    WHILE @current_level < p_max_level AND ROW_COUNT() > 0 DO
        SET @current_level = @current_level + 1;
        
        INSERT INTO temp_user_hierarchy (user_id, level_num)
        SELECT u.id, @current_level
        FROM vim_user u
        INNER JOIN temp_user_hierarchy th ON u.invite_user = th.user_id
        WHERE th.level_num = @current_level - 1
        AND u.id NOT IN (SELECT user_id FROM temp_user_hierarchy);
    END WHILE;
    
    -- 统计总用户数
    SELECT COUNT(DISTINCT user_id) INTO v_total_users
    FROM temp_user_hierarchy;
    
    -- 统计总充值金额
    SELECT COALESCE(SUM(vor.amount), 0.00) INTO v_total_recharge
    FROM vim_order_recharge vor
    INNER JOIN temp_user_hierarchy th ON vor.uid = th.user_id
    WHERE vor.create_time BETWEEN v_start_timestamp AND v_end_timestamp
    AND vor.state = 2; -- 已支付状态
    
    -- 统计总消费金额（通过用户金币变动记录）
    SELECT COALESCE(SUM(ABS(vcl.coin)), 0.00) INTO v_total_consume
    FROM vim_user_coin_log vcl
    INNER JOIN temp_user_hierarchy th ON vcl.uid = th.user_id
    WHERE vcl.time BETWEEN v_start_timestamp AND v_end_timestamp
    AND vcl.type = 2  -- 消费类型
    AND vcl.coin < 0; -- 消费是负数
    
    -- 计算总利润（总充值 - 总消费）
    SET v_total_profit = v_total_recharge - v_total_consume;
    
    -- 返回结果
    SELECT 
        CAST(v_total_users AS CHAR) AS zongyonghu,
        v_total_recharge AS zongchongzhi,
        v_total_profit AS zonglirun;
    
    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_user_hierarchy;
    
END$$

DELIMITER ;

-- 使用示例：
-- CALL GetSubordinateProfit(1, 5, '1640995200', '1672531199');
-- 参数说明：
-- 1: 根用户ID
-- 5: 最大查询层级
-- '1640995200': 开始时间戳（字符串格式）
-- '1672531199': 结束时间戳（字符串格式）
