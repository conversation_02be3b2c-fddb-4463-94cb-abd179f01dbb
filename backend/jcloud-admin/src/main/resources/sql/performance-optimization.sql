-- 登录性能优化SQL脚本
-- 用于检查和创建必要的数据库索引

-- 1. 检查sys_user表的索引情况
SHOW INDEX FROM sys_user;

-- 2. 检查是否存在username和tenant_id的复合索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'sys_user'
    AND COLUMN_NAME IN ('username', 'tenant_id')
ORDER BY 
    INDEX_NAME, SEQ_IN_INDEX;

-- 3. 如果不存在，创建复合索引（提高登录查询性能）
-- 注意：执行前请先检查是否已存在类似索引
CREATE INDEX IF NOT EXISTS idx_user_username_tenant 
ON sys_user (username, tenant_id, deleted);

-- 4. 检查用户角色关联表的索引
SHOW INDEX FROM sys_user_role;

-- 5. 如果需要，创建用户角色查询索引
CREATE INDEX IF NOT EXISTS idx_user_role_user_tenant 
ON sys_user_role (user_id, tenant_id);

-- 6. 检查角色权限关联表的索引
SHOW INDEX FROM sys_role_menu;

-- 7. 如果需要，创建角色权限查询索引
CREATE INDEX IF NOT EXISTS idx_role_menu_role_tenant 
ON sys_role_menu (role_id, tenant_id);

-- 8. 分析表统计信息（可选，用于优化查询计划）
ANALYZE TABLE sys_user;
ANALYZE TABLE sys_user_role;
ANALYZE TABLE sys_role_menu;

-- 9. 检查登录相关查询的执行计划
-- 用户查询
EXPLAIN SELECT * FROM sys_user 
WHERE username = 'admin' AND tenant_id = 1 AND deleted = 0;

-- 用户角色查询
EXPLAIN SELECT r.* FROM sys_role r
INNER JOIN sys_user_role ur ON r.id = ur.role_id
WHERE ur.user_id = 1 AND ur.tenant_id = 1 AND r.deleted = 0;

-- 角色权限查询
EXPLAIN SELECT m.* FROM sys_menu m
INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
WHERE rm.role_id = 1 AND rm.tenant_id = 1 AND m.deleted = 0;

-- 10. 性能监控查询
-- 查看慢查询日志设置
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW VARIABLES LIKE 'long_query_time';

-- 11. 连接状态检查
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Threads_running';

-- 12. 查看当前连接情况
SHOW PROCESSLIST;
