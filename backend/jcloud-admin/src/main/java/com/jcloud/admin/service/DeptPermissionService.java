package com.jcloud.admin.service;

import com.jcloud.common.annotation.DataSource;

import java.util.List;
import java.util.Set;

/**
 * 部门权限服务接口
 * 提供完整的部门权限检查和管理功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DeptPermissionService {
    
    /**
     * 获取用户的主部门ID
     * 
     * @param userId 用户ID
     * @return 主部门ID，如果没有则返回null
     */
    Long getUserPrimaryDeptId(Long userId);
    
    /**
     * 获取用户所属的所有部门ID列表
     * 
     * @param userId 用户ID
     * @return 部门ID列表
     */
    List<Long> getUserDeptIds(Long userId);
    
    /**
     * 检查两个用户是否在同一部门
     * 
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 是否在同一部门
     */
    boolean isUsersInSameDept(Long userId1, Long userId2);
    
    /**
     * 检查用户是否在指定部门中
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 是否在指定部门中
     */
    boolean isUserInDept(Long userId, Long deptId);
    
    /**
     * 获取部门及其所有子部门的ID列表
     * 
     * @param deptId 部门ID
     * @return 部门ID列表（包含自身和所有子部门）
     */
    List<Long> getDeptAndSubDeptIds(Long deptId);
    
    /**
     * 检查用户是否可以访问指定部门的数据
     * 
     * @param userId 用户ID
     * @param targetDeptId 目标部门ID
     * @param dataScope 数据权限范围
     * @return 是否有权限访问
     */
    boolean canAccessDept(Long userId, Long targetDeptId, String dataScope);
    
    /**
     * 检查用户是否可以访问指定用户的数据
     * 
     * @param currentUserId 当前用户ID
     * @param targetUserId 目标用户ID
     * @param dataScope 数据权限范围
     * @return 是否有权限访问
     */
    boolean canAccessUser(Long currentUserId, Long targetUserId, String dataScope);
    
    /**
     * 根据数据权限范围获取用户可访问的部门ID列表
     * 
     * @param userId 用户ID
     * @param dataScope 数据权限范围
     * @return 可访问的部门ID列表
     */
    List<Long> getAccessibleDeptIds(Long userId, String dataScope);
    
    /**
     * 根据数据权限范围获取用户可访问的用户ID列表
     * 
     * @param userId 用户ID
     * @param dataScope 数据权限范围
     * @return 可访问的用户ID列表
     */
    @DataSource
    List<Long> getAccessibleUserIds(Long userId, String dataScope);
    
    /**
     * 批量检查用户权限
     * 
     * @param currentUserId 当前用户ID
     * @param targetUserIds 目标用户ID列表
     * @param dataScope 数据权限范围
     * @return 有权限访问的用户ID集合
     */
    Set<Long> filterAccessibleUserIds(Long currentUserId, List<Long> targetUserIds, String dataScope);
    
    /**
     * 设置用户的主部门
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 是否设置成功
     */
    boolean setPrimaryDept(Long userId, Long deptId);
    
    /**
     * 为用户添加部门关联
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param isMain 是否为主部门
     */
    void addUserDept(Long userId, Long deptId, boolean isMain);
    
    /**
     * 移除用户的部门关联
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 是否移除成功
     */
    boolean removeUserDept(Long userId, Long deptId);

    /**
     * 批量分配用户到部门
     *
     * @param deptId 部门ID
     * @param userIds 用户ID列表
     * @param isMain 是否设置为主部门
     * @return 成功分配的用户数量
     */
    int batchAssignUsersToDept(Long deptId, List<Long> userIds, boolean isMain);

    /**
     * 批量从部门移除用户
     *
     * @param deptId 部门ID
     * @param userIds 用户ID列表
     * @return 成功移除的用户数量
     */
    int batchRemoveUsersFromDept(Long deptId, List<Long> userIds);
    
    /**
     * 获取部门下的所有用户ID（包括子部门）
     * 
     * @param deptId 部门ID
     * @param includeSubDepts 是否包括子部门
     * @return 用户ID列表
     */
    List<Long> getDeptUserIds(Long deptId, boolean includeSubDepts);
}
