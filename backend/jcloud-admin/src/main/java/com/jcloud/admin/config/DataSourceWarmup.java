package com.jcloud.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 数据源预热组件
 * 应用启动时预热数据库连接池，提高首次登录性能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataSourceWarmup implements ApplicationRunner {
    
    @Qualifier("masterDataSource")
    private final DataSource masterDataSource;
    
    @Qualifier("slaveDataSource")
    private final DataSource slaveDataSource;
    
    private final ExecutorService warmupExecutor = Executors.newFixedThreadPool(4);
    
    @Override
    public void run(ApplicationArguments args) {
        log.info("开始数据源预热...");
        
        long startTime = System.currentTimeMillis();
        
        // 并行预热主库和从库
        CompletableFuture<Void> masterWarmup = CompletableFuture.runAsync(() -> 
            warmupDataSource("主库", masterDataSource), warmupExecutor);
            
        CompletableFuture<Void> slaveWarmup = CompletableFuture.runAsync(() -> 
            warmupDataSource("从库", slaveDataSource), warmupExecutor);
        
        // 等待预热完成
        CompletableFuture.allOf(masterWarmup, slaveWarmup)
            .thenRun(() -> {
                long endTime = System.currentTimeMillis();
                log.info("数据源预热完成，耗时: {}ms", endTime - startTime);
                warmupExecutor.shutdown();
            })
            .exceptionally(throwable -> {
                log.error("数据源预热失败", throwable);
                warmupExecutor.shutdown();
                return null;
            });
    }
    
    /**
     * 预热单个数据源
     */
    private void warmupDataSource(String name, DataSource dataSource) {
        try {
            log.info("开始预热{}", name);
            
            if (dataSource instanceof DruidDataSource) {
                DruidDataSource druidDataSource = (DruidDataSource) dataSource;
                
                // 记录预热前状态
                int initialActive = druidDataSource.getActiveCount();
                int initialIdle = druidDataSource.getPoolingCount();
                log.info("{} 预热前状态 - 活跃连接: {}, 空闲连接: {}", name, initialActive, initialIdle);
                
                // 创建多个连接进行预热
                int warmupConnections = Math.min(druidDataSource.getInitialSize(), 5);
                for (int i = 0; i < warmupConnections; i++) {
                    warmupSingleConnection(name, dataSource, i + 1);
                }
                
                // 记录预热后状态
                int finalActive = druidDataSource.getActiveCount();
                int finalIdle = druidDataSource.getPoolingCount();
                log.info("{} 预热后状态 - 活跃连接: {}, 空闲连接: {}", name, finalActive, finalIdle);
            }
            
        } catch (Exception e) {
            log.error("{} 预热失败", name, e);
        }
    }
    
    /**
     * 预热单个连接
     */
    private void warmupSingleConnection(String dataSourceName, DataSource dataSource, int connectionIndex) {
        try (Connection connection = dataSource.getConnection()) {
            // 执行简单查询验证连接
            try (PreparedStatement statement = connection.prepareStatement("SELECT 1");
                 ResultSet resultSet = statement.executeQuery()) {
                
                if (resultSet.next()) {
                    log.debug("{} 连接#{} 预热成功", dataSourceName, connectionIndex);
                }
            }
            
            // 模拟登录查询，预热相关表
            if ("主库".equals(dataSourceName)) {
                warmupLoginQueries(connection);
            }
            
        } catch (Exception e) {
            log.warn("{} 连接#{} 预热失败: {}", dataSourceName, connectionIndex, e.getMessage());
        }
    }
    
    /**
     * 预热登录相关查询
     */
    private void warmupLoginQueries(Connection connection) {
        try {
            // 预热用户查询
            try (PreparedStatement statement = connection.prepareStatement(
                "SELECT COUNT(*) FROM sys_user WHERE deleted = 0 LIMIT 1")) {
                statement.executeQuery();
                log.debug("用户表查询预热完成");
            }
            
            // 预热角色查询
            try (PreparedStatement statement = connection.prepareStatement(
                "SELECT COUNT(*) FROM sys_role WHERE deleted = 0 LIMIT 1")) {
                statement.executeQuery();
                log.debug("角色表查询预热完成");
            }
            
            // 预热权限查询
            try (PreparedStatement statement = connection.prepareStatement(
                "SELECT COUNT(*) FROM sys_menu WHERE deleted = 0 LIMIT 1")) {
                statement.executeQuery();
                log.debug("菜单表查询预热完成");
            }
            
        } catch (Exception e) {
            log.debug("登录查询预热失败，但不影响系统运行: {}", e.getMessage());
        }
    }
}
