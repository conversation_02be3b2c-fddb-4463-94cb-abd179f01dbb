package com.jcloud.admin.aspect;

import com.jcloud.common.annotation.DataSource;
import com.jcloud.common.config.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据源切换切面
 * 根据@DataSource注解自动切换数据源
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Component
@Order(1) // 确保在事务切面之前执行
@Slf4j
public class DataSourceAspect {
    
    /**
     * 切点：匹配所有标注了@DataSource注解的方法
     */
    @Pointcut("@annotation(com.jcloud.common.annotation.DataSource) || @within(com.jcloud.common.annotation.DataSource)")
    public void dataSourcePointcut() {}
    
    /**
     * 环绕通知：在方法执行前切换数据源，执行后恢复
     * 增强异常处理和连接管理
     */
    @Around("dataSourcePointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取数据源注解
        DataSource dataSource = getDataSource(point);

        if (dataSource != null) {
            String dataSourceName = dataSource.value();
            String originalDataSource = DataSourceContextHolder.getDataSource();

            try {
                // 切换数据源
                DataSourceContextHolder.setDataSource(dataSourceName);
                log.debug("切换到数据源: {} for method: {}", dataSourceName, point.getSignature().toShortString());

                // 执行目标方法
                return point.proceed();

            } catch (Exception e) {
                // 记录数据源相关的异常信息
                log.error("数据源 {} 执行方法 {} 时发生异常: {}",
                         dataSourceName, point.getSignature().toShortString(), e.getMessage());

                // 如果是连接相关异常，尝试重试一次
                if (isConnectionException(e) && shouldRetry(point)) {
                    log.warn("检测到连接异常，尝试重试方法: {}", point.getSignature().toShortString());
                    try {
                        // 短暂等待后重试
                        Thread.sleep(100);
                        return point.proceed();
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("方法执行被中断", ie);
                    }
                }

                throw e;
            } finally {
                // 恢复原始数据源设置
                if (originalDataSource != null) {
                    DataSourceContextHolder.setDataSource(originalDataSource);
                } else {
                    DataSourceContextHolder.clearDataSource();
                }
                log.debug("恢复数据源设置 for method: {}", point.getSignature().toShortString());
            }
        }

        // 没有注解，使用默认数据源
        return point.proceed();
    }

    /**
     * 判断是否为连接相关异常
     */
    private boolean isConnectionException(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        return message.contains("statement closed") ||
               message.contains("connection closed") ||
               message.contains("Connection is closed") ||
               message.contains("No operations allowed after statement closed") ||
               message.contains("Communications link failure");
    }

    /**
     * 判断是否应该重试
     * 只对查询操作进行重试，避免重复执行写操作
     */
    private boolean shouldRetry(ProceedingJoinPoint point) {
        String methodName = point.getSignature().getName().toLowerCase();
        return methodName.startsWith("get") ||
               methodName.startsWith("find") ||
               methodName.startsWith("select") ||
               methodName.startsWith("query") ||
               methodName.startsWith("count");
    }
    
    /**
     * 获取数据源注解
     * 优先从方法上获取，如果方法上没有则从类上获取
     */
    private DataSource getDataSource(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        
        // 先从方法上查找注解
        DataSource dataSource = AnnotationUtils.findAnnotation(method, DataSource.class);
        if (dataSource != null) {
            return dataSource;
        }
        
        // 再从类上查找注解
        return AnnotationUtils.findAnnotation(point.getTarget().getClass(), DataSource.class);
    }
}