package com.jcloud.admin.service;

import com.jcloud.common.dto.RoleCreateRequest;
import com.jcloud.common.dto.RoleQueryRequest;
import com.jcloud.common.dto.RoleUpdateRequest;
import com.jcloud.common.entity.SysRole;

import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;

import java.util.List;
import java.util.Set;

/**
 * 角色服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysRoleService extends BaseService<SysRole> {
    
    /**
     * 分页查询角色列表
     * @param queryRequest 查询条件
     * @return 角色分页列表
     */
    PageResult<SysRole> pageRoles(RoleQueryRequest queryRequest);
    
    /**
     * 创建角色
     * @param createRequest 创建请求
     * @return 是否成功
     */
    boolean createRole(RoleCreateRequest createRequest);
    
    /**
     * 更新角色
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateRole(RoleUpdateRequest updateRequest);
    
    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole getRoleByCode(String roleCode);
    
    /**
     * 检查角色编码是否存在
     * 
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleCodeExists(String roleCode, Long excludeRoleId);
    
    /**
     * 检查角色名称是否存在
     * 
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleNameExists(String roleName, Long excludeRoleId);
    
    /**
     * 启用/禁用角色
     * 
     * @param roleId 角色ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateRoleStatus(Long roleId, Integer status);
    
    /**
     * 批量启用/禁用角色
     * 
     * @param roleIds 角色ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean updateRoleStatusBatch(List<Long> roleIds, Integer status);
    
    // 注意：权限分配功能已整合到菜单分配中，不再需要独立的权限分配方法
    
    /**
     * 根据用户ID获取角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> getRolesByUserId(Long userId);
    
    /**
     * 根据用户ID获取角色编码列表
     *
     * @param userId 用户ID
     * @return 角色编码列表
     */
    Set<String> getRoleCodesByUserId(Long userId);
    
    /**
     * 获取所有启用的角色
     * 
     * @return 角色列表
     */
    List<SysRole> getAllEnabledRoles();
    
    /**
     * 统计角色下的用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    int countUsersByRoleId(Long roleId);
    
    /**
     * 删除角色（检查是否有用户关联）
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long roleId);
    
    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean deleteRolesBatch(List<Long> roleIds);

    /**
     * 为角色分配菜单
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean assignMenus(Long roleId, List<Long> menuIds);

    /**
     * 获取角色菜单列表
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> getRoleMenus(Long roleId);

    /**
     * 获取角色菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> getRoleMenuIds(Long roleId);
}
