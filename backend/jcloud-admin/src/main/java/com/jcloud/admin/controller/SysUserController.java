package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.vo.SysUserVO;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.dto.UserCreateRequest;
import com.jcloud.common.dto.UserQueryRequest;
import com.jcloud.common.dto.UserUpdateRequest;
import com.jcloud.common.entity.SysUser;
import com.jcloud.admin.vo.SysUserDetailVO;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "用户管理", description = "用户管理相关接口")
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
@Slf4j
public class SysUserController {
    
    private final SysUserService userService;
    
    @Operation(summary = "分页查询用户列表", description = "根据条件分页查询用户列表（脱敏版本）")
    @SaCheckPermission("system:user:list")
    @GetMapping("/page")
    public Result<PageResult<SysUserVO>> pageUsers(@Valid UserQueryRequest queryRequest) {
        PageResult<SysUserVO> pageResult = userService.pageUsersForDisplay(queryRequest);
        return Result.success("查询用户列表成功", pageResult);
    }
    
    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详细信息（脱敏版本）")
    @SaCheckPermission("system:user:query")
    @GetMapping("/{id}")
    public Result<SysUserDetailVO> getUserById(@Parameter(description = "用户ID") @PathVariable("id") Long id) {
        SysUserDetailVO userDetail = userService.getUserDetailById(id);
        if (userDetail == null) {
            return Result.error("用户不存在");
        }
        return Result.success("获取用户详情成功", userDetail);
    }

    @Operation(summary = "获取用户编辑信息", description = "根据用户ID获取用户详细信息（原始版本，用于编辑）")
    @SaCheckPermission("system:user:edit")
    @GetMapping("/{id}/edit")
    public Result<SysUserDetailVO> getUserDetailForEdit(@Parameter(description = "用户ID") @PathVariable("id") Long id) {
        SysUserDetailVO userDetail = userService.getUserDetailForEdit(id);
        if (userDetail == null) {
            return Result.error("用户不存在");
        }
        return Result.success("获取用户编辑信息成功", userDetail);
    }
    
    @Operation(summary = "创建用户", description = "创建新用户")
    @SaCheckPermission("system:user:add")
    @PostMapping
    public Result<Void> createUser(@Valid @RequestBody UserCreateRequest createRequest) {
        boolean success = userService.createUser(createRequest);
        if (success) {
            return Result.<Void>success("创建用户成功", null);
        } else {
            return Result.error("创建用户失败");
        }
    }
    
    @Operation(summary = "更新用户", description = "更新用户信息")
    @SaCheckPermission("system:user:edit")
    @PutMapping
    public Result<Void> updateUser(@Valid @RequestBody UserUpdateRequest updateRequest) {
        boolean success = userService.updateUser(updateRequest);
        if (success) {
            return Result.<Void>success("更新用户成功", null);
        } else {
            return Result.error("更新用户失败");
        }
    }
    
    @Operation(summary = "删除用户", description = "根据用户ID删除用户（逻辑删除）")
    @SaCheckPermission("system:user:remove")
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(@Parameter(description = "用户ID") @PathVariable("id") Long id) {
        boolean success = userService.removeById(id);
        if (success) {
            return Result.<Void>success("删除用户成功", null);
        } else {
            return Result.error("删除用户失败");
        }
    }
    
    @Operation(summary = "批量删除用户", description = "根据用户ID列表批量删除用户")
    @SaCheckPermission("system:user:remove")
    @DeleteMapping("/batch")
    public Result<Void> deleteUsersBatch(@RequestBody List<Long> userIds) {
        boolean success = userService.removeByIds(userIds);
        if (success) {
            return Result.<Void>success("批量删除用户成功", null);
        } else {
            return Result.error("批量删除用户失败");
        }
    }
    
    @Operation(summary = "重置用户密码", description = "重置用户密码")
    @SaCheckPermission("system:user:resetPwd")
    @PostMapping("/{id}/reset-password")
    public Result<Void> resetPassword(
            @Parameter(description = "用户ID") @PathVariable("id") Long id,
            @RequestBody(required = false) Map<String, String> requestBody) {

        // 获取新密码，如果没有提供则使用默认密码
        String newPassword = CommonConstants.DEFAULT_PASSWORD;
        if (requestBody != null && requestBody.containsKey("newPassword")) {
            String providedPassword = requestBody.get("newPassword");
            if (providedPassword != null && !providedPassword.trim().isEmpty()) {
                newPassword = providedPassword.trim();
            }
        }

        boolean success = userService.resetPassword(id, newPassword);
        if (success) {
            return Result.<Void>success("重置密码成功", null);
        } else {
            return Result.error("重置密码失败");
        }
    }
    
    @Operation(summary = "修改用户密码", description = "用户修改自己的密码")
    @PutMapping("/change-password")
    public Result<Void> changePassword(@RequestBody Map<String, String> passwordMap) {
        String oldPassword = passwordMap.get("oldPassword");
        String newPassword = passwordMap.get("newPassword");
        
        if (oldPassword == null || newPassword == null) {
            return Result.error("参数不能为空");
        }
        
        // 获取当前用户ID（这里需要从SecurityUtils获取）
        Long userId = 1L; // TODO: 从当前登录用户获取
        
        boolean success = userService.changePassword(userId, oldPassword, newPassword);
        if (success) {
            return Result.<Void>success("修改密码成功", null);
        } else {
            return Result.error("修改密码失败");
        }
    }
    
    @Operation(summary = "启用/禁用用户", description = "启用或禁用用户账号")
    @SaCheckPermission("system:user:edit")
    @PutMapping("/{id}/status")
    public Result<Void> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable("id") Long id,
            @Parameter(description = "状态") @RequestParam("status") Integer status) {
        
        boolean success = userService.updateUserStatus(id, status);
        if (success) {
            return Result.<Void>success("更新用户状态成功", null);
        } else {
            return Result.error("更新用户状态失败");
        }
    }
    
    @Operation(summary = "批量启用/禁用用户", description = "批量启用或禁用用户账号")
    @SaCheckPermission("system:user:edit")
    @PutMapping("/batch/status")
    public Result<Void> updateUserStatusBatch(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) params.get("userIds");
        Integer status = (Integer) params.get("status");
        
        if (userIds == null || userIds.isEmpty() || status == null) {
            return Result.error("参数不能为空");
        }
        
        boolean success = userService.updateUserStatusBatch(userIds, status);
        if (success) {
            return Result.<Void>success("批量更新用户状态成功", null);
        } else {
            return Result.error("批量更新用户状态失败");
        }
    }
    
    @Operation(summary = "分配用户角色", description = "为用户分配角色")
    @SaCheckPermission("system:user:edit")
    @PutMapping("/{id}/roles")
    public Result<Void> assignRoles(
            @Parameter(description = "用户ID") @PathVariable("id") Long id,
            @RequestBody List<Long> roleIds) {
        
        boolean success = userService.assignRoles(id, roleIds);
        if (success) {
            return Result.<Void>success("分配用户角色成功", null);
        } else {
            return Result.error("分配用户角色失败");
        }
    }
    
    @Operation(summary = "获取用户角色", description = "获取用户拥有的角色列表")
    @SaCheckPermission("system:user:query")
    @GetMapping("/{id}/roles")
    public Result<List<Long>> getUserRoles(@Parameter(description = "用户ID") @PathVariable("id") Long id) {
        List<Long> roleIds = userService.getUserRoleIds(id);
        return Result.success("获取用户角色成功", roleIds);
    }
    
    @Operation(summary = "检查用户名是否存在", description = "检查用户名是否已被使用")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(
            @Parameter(description = "用户名") @RequestParam String username,
            @Parameter(description = "排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = userService.isUsernameExists(username, excludeUserId);
        return Result.success("检查用户名完成", exists);
    }
    
    @Operation(summary = "检查邮箱是否存在", description = "检查邮箱是否已被使用")
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(
            @Parameter(description = "邮箱") @RequestParam String email,
            @Parameter(description = "排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = userService.isEmailExists(email, excludeUserId);
        return Result.success("检查邮箱完成", exists);
    }
    
    @Operation(summary = "检查手机号是否存在", description = "检查手机号是否已被使用")
    @GetMapping("/check-phone")
    public Result<Boolean> checkPhone(
            @Parameter(description = "手机号") @RequestParam String phone,
            @Parameter(description = "排除的用户ID") @RequestParam(required = false) Long excludeUserId) {

        boolean exists = userService.isPhoneExists(phone, excludeUserId);
        return Result.success("检查手机号完成", exists);
    }

    @Operation(summary = "同步主播用户", description = "从vim_user表同步主播用户到sys_user表")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/sync-anchor-users")
    public Result<String> syncAnchorUsers() {
        try {
            String result = userService.syncAnchorUsers();
            return Result.success("用户同步操作完成", result);
        } catch (Exception e) {
            log.error("同步主播用户失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    @Operation(summary = "同步代理用户", description = "从vim_user表同步代理用户到sys_user表")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/sync-agent-users")
    public Result<String> syncAgentUsers() {
        try {
            String result = userService.syncAgentUsers();
            return Result.success("代理用户同步操作完成", result);
        } catch (Exception e) {
            log.error("同步代理用户失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    @Operation(summary = "同步主播和代理用户", description = "从vim_user表同步主播和代理用户到sys_user表")
    @SaCheckPermission("system:user:sync")
    @PostMapping("/sync-anchor-and-agent-users")
    public Result<String> syncAnchorAndAgentUsers() {
        try {
            String result = userService.syncAnchorAndAgentUsers();
            return Result.success("主播和代理用户同步操作完成", result);
        } catch (Exception e) {
            log.error("同步主播和代理用户失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    @Operation(summary = "测试同步主播用户", description = "测试同步功能（无需认证）")
    @PostMapping("/test-sync-anchor-users")
    public Result<String> testSyncAnchorUsers() {
        try {
            log.info("开始测试同步主播用户功能...");
            String result = userService.syncAnchorUsers();
            return Result.success("同步测试完成", result);
        } catch (Exception e) {
            log.error("同步测试失败", e);
            return Result.error("同步测试失败: " + e.getMessage());
        }
    }
}
