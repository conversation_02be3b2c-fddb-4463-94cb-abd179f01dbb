package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量部门用户操作请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "批量部门用户操作请求")
public class BatchDeptUserRequest {

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long deptId;

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @Schema(description = "用户ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> userIds;

    /**
     * 是否设置为主部门（仅在分配时有效）
     */
    @Schema(description = "是否设置为主部门", defaultValue = "false")
    private Boolean isMain = false;
}
