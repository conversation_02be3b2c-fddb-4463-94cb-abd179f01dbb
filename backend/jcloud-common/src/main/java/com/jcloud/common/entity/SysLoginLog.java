package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 登录日志实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_login_log")
@Schema(description = "登录日志信息")
public class SysLoginLog extends BaseEntity {
    
    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String userName;
    
    /**
     * 登录IP地址
     */
    @Schema(description = "登录IP地址")
    private String ipaddr;
    
    /**
     * 登录地点
     */
    @Schema(description = "登录地点")
    private String loginLocation;
    
    /**
     * 浏览器类型
     */
    @Schema(description = "浏览器类型")
    private String browser;
    
    /**
     * 操作系统
     */
    @Schema(description = "操作系统")
    private String os;
    
    /**
     * 登录状态（0-成功，1-失败）
     */
    @Schema(description = "登录状态")
    private Integer status;
    
    /**
     * 提示消息
     */
    @Schema(description = "提示消息")
    private String msg;
    
    /**
     * 访问时间（时间戳，秒）
     */
    @Schema(description = "访问时间")
    private Long loginTime;
}
