package com.jcloud.common.constant;

/**
 * 公共常量类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CommonConstants {
    
    /**
     * 成功标识
     */
    String SUCCESS = "success";
    
    /**
     * 失败标识
     */
    String FAIL = "fail";
    
    /**
     * 默认页码
     */
    Integer DEFAULT_PAGE_NUM = 1;
    
    /**
     * 默认每页大小
     */
    Integer DEFAULT_PAGE_SIZE = 10;
    
    /**
     * 最大每页大小
     */
    Integer MAX_PAGE_SIZE = 100;
    
    /**
     * 默认排序方向
     */
    String DEFAULT_ORDER_DIRECTION = "DESC";
    
    /**
     * 升序
     */
    String ORDER_ASC = "ASC";
    
    /**
     * 降序
     */
    String ORDER_DESC = "DESC";
    
    /**
     * 逻辑删除 - 未删除
     */
    Integer NOT_DELETED = 0;
    
    /**
     * 逻辑删除 - 已删除
     */
    Integer DELETED = 1;
    
    /**
     * 状态 - 启用
     */
    Integer STATUS_ENABLED = 1;
    
    /**
     * 状态 - 禁用
     */
    Integer STATUS_DISABLED = 0;
    
    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "123456";
    
    /**
     * 超级管理员角色编码
     */
    String SUPER_ADMIN_ROLE = "SUPER_ADMIN";
    
    /**
     * 管理员角色编码
     */
    String ADMIN_ROLE = "ADMIN";
    
    /**
     * 普通用户角色编码
     */
    String USER_ROLE = "USER";

    /**
     * 主播角色编码
     */
    String ANCHOR_ROLE = "ANCHOR";

    /**
     * 代理角色编码
     */
    String AGENT_ROLE = "AGENT";
    
    /**
     * 默认租户ID
     */
    Long DEFAULT_TENANT_ID = 1L;
    
    /**
     * 系统用户ID
     */
    Long SYSTEM_USER_ID = 0L;
    
    /**
     * 根菜单ID
     */
    Long ROOT_MENU_ID = 0L;
    
    /**
     * 菜单类型 - 目录
     */
    Integer MENU_TYPE_CATALOG = 0;
    
    /**
     * 菜单类型 - 菜单
     */
    Integer MENU_TYPE_MENU = 1;
    
    /**
     * 菜单类型 - 按钮
     */
    Integer MENU_TYPE_BUTTON = 2;
    
    /**
     * 权限类型 - 菜单权限
     */
    String PERMISSION_TYPE_MENU = "MENU";
    
    /**
     * 权限类型 - 按钮权限
     */
    String PERMISSION_TYPE_BUTTON = "BUTTON";
    
    /**
     * 权限类型 - API权限
     */
    String PERMISSION_TYPE_API = "API";
}
