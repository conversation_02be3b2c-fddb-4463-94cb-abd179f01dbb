package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运营统计数据响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "运营统计数据响应")
public class OperationsStatsResponse {

    @Schema(description = "总用户数", example = "50")
    private Long totalUsers;

    @Schema(description = "本月新增用户数", example = "12")
    private Long newUsersThisMonth;

    @Schema(description = "本月总充值金额", example = "10000.00")
    private java.math.BigDecimal totalRechargeThisMonth;

    @Schema(description = "本月总消费金额", example = "8500.00")
    private java.math.BigDecimal totalConsumeThisMonth;

    @Schema(description = "今日充值金额", example = "350.00")
    private java.math.BigDecimal todayRecharge;

    @Schema(description = "统计时间", example = "2024-01-15 10:30:00")
    private java.time.LocalDateTime statsTime;
}
