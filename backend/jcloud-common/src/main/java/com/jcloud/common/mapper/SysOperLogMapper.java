package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysOperLog;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysOperLogMapper extends BaseMapper<SysOperLog> {
    
    /**
     * 根据操作人员查询操作日志
     * 
     * @param operName 操作人员
     * @param tenantId 租户ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM sys_oper_log WHERE oper_name = #{operName} AND tenant_id = #{tenantId} " +
            "ORDER BY oper_time DESC LIMIT 100")
    List<SysOperLog> selectByOperName(@Param("operName") String operName, @Param("tenantId") Long tenantId);
    
    /**
     * 根据业务类型查询操作日志
     * 
     * @param businessType 业务类型
     * @param tenantId 租户ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM sys_oper_log WHERE business_type = #{businessType} AND tenant_id = #{tenantId} " +
            "ORDER BY oper_time DESC")
    List<SysOperLog> selectByBusinessType(@Param("businessType") Integer businessType, @Param("tenantId") Long tenantId);
    
    /**
     * 根据操作状态查询操作日志
     * 
     * @param status 操作状态
     * @param tenantId 租户ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM sys_oper_log WHERE status = #{status} AND tenant_id = #{tenantId} " +
            "ORDER BY oper_time DESC")
    List<SysOperLog> selectByStatus(@Param("status") Integer status, @Param("tenantId") Long tenantId);
    
    /**
     * 统计指定时间范围内的操作日志数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 日志数量
     */
    @Select("SELECT COUNT(1) FROM sys_oper_log " +
            "WHERE oper_time >= #{startTime} AND oper_time <= #{endTime} AND tenant_id = #{tenantId}")
    int countByTimeRange(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime, 
                        @Param("tenantId") Long tenantId);
    
    /**
     * 统计各业务类型的操作数量
     * 
     * @param tenantId 租户ID
     * @return 统计结果
     */
    @Select("SELECT business_type, COUNT(1) as count FROM sys_oper_log " +
            "WHERE tenant_id = #{tenantId} GROUP BY business_type")
    @MapKey("business_type")
    List<java.util.Map<String, Object>> countByBusinessType(@Param("tenantId") Long tenantId);
    
    /**
     * 清理指定天数之前的操作日志
     * 
     * @param days 保留天数
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM sys_oper_log WHERE oper_time < DATE_SUB(NOW(), INTERVAL #{days} DAY) AND tenant_id = #{tenantId}")
    int cleanOldLogs(@Param("days") int days, @Param("tenantId") Long tenantId);
    
    /**
     * 获取最近的操作日志
     * 
     * @param limit 限制数量
     * @param tenantId 租户ID
     * @return 操作日志列表
     */
    @Select("SELECT * FROM sys_oper_log WHERE tenant_id = #{tenantId} " +
            "ORDER BY oper_time DESC LIMIT #{limit}")
    List<SysOperLog> selectRecentLogs(@Param("limit") int limit, @Param("tenantId") Long tenantId);
    
    /**
     * 统计今日操作数量
     * 
     * @param tenantId 租户ID
     * @return 操作数量
     */
    @Select("SELECT COUNT(1) FROM sys_oper_log " +
            "WHERE DATE(oper_time) = CURDATE() AND tenant_id = #{tenantId}")
    int countTodayOpers(@Param("tenantId") Long tenantId);
    
    /**
     * 统计异常操作数量
     * 
     * @param tenantId 租户ID
     * @return 异常操作数量
     */
    @Select("SELECT COUNT(1) FROM sys_oper_log " +
            "WHERE status = 1 AND tenant_id = #{tenantId}")
    int countErrorOpers(@Param("tenantId") Long tenantId);
}
