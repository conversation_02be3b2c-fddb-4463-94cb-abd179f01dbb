package com.jcloud.common.entity;

import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 操作日志实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_oper_log")
@Schema(description = "操作日志信息")
public class SysOperLog extends BaseEntity {
    
    /**
     * 操作模块
     */
    @Schema(description = "操作模块")
    private String title;
    
    /**
     * 业务类型（0-其它，1-新增，2-修改，3-删除，4-授权，5-导出，6-导入，7-强退，8-生成代码，9-清空数据）
     */
    @Schema(description = "业务类型")
    private Integer businessType;
    
    /**
     * 请求方法
     */
    @Schema(description = "请求方法")
    private String method;
    
    /**
     * 请求方式
     */
    @Schema(description = "请求方式")
    private String requestMethod;
    
    /**
     * 操作类别（0-其它，1-后台用户，2-手机端用户）
     */
    @Schema(description = "操作类别")
    private Integer operatorType;
    
    /**
     * 操作人员
     */
    @Schema(description = "操作人员")
    private String operName;
    
    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;
    
    /**
     * 请求URL
     */
    @Schema(description = "请求URL")
    private String operUrl;
    
    /**
     * 主机地址
     */
    @Schema(description = "主机地址")
    private String operIp;
    
    /**
     * 操作地点
     */
    @Schema(description = "操作地点")
    private String operLocation;
    
    /**
     * 请求参数
     */
    @Schema(description = "请求参数")
    private String operParam;
    
    /**
     * 返回参数
     */
    @Schema(description = "返回参数")
    private String jsonResult;
    
    /**
     * 操作状态（0-正常，1-异常）
     */
    @Schema(description = "操作状态")
    private Integer status;
    
    /**
     * 错误消息
     */
    @Schema(description = "错误消息")
    private String errorMsg;
    
    /**
     * 操作时间（时间戳，秒）
     */
    @Schema(description = "操作时间")
    private Long operTime;
    
    /**
     * 消耗时间（毫秒）
     */
    @Schema(description = "消耗时间")
    private Long costTime;
}
