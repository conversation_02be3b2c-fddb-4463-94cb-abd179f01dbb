package com.jcloud.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 角色编码解析工具类
 * 用于解析代理角色编码，提取查询层数限制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class RoleCodeUtils {
    
    /**
     * 代理角色编码正则表达式
     * 匹配格式：daili_N（其中N为数字）
     */
    private static final Pattern DAILI_PATTERN = Pattern.compile("^daili_(\\d+)$", Pattern.CASE_INSENSITIVE);
    
    /**
     * 默认查询层数
     */
    private static final int DEFAULT_QUERY_LEVEL = 1;
    
    /**
     * 最大查询层数限制
     */
    private static final int MAX_QUERY_LEVEL = 0;
    
    /**
     * 最小查询层数限制
     */
    private static final int MIN_QUERY_LEVEL = 1;
    
    /**
     * 从角色编码集合中解析查询层数
     * 
     * @param roleCodes 角色编码集合
     * @return 查询层数（1-10之间的整数）
     */
    public static int parseQueryLevel(Set<String> roleCodes) {
        if (CollUtil.isEmpty(roleCodes)) {
            log.debug("角色编码集合为空，使用默认查询层数：{}", DEFAULT_QUERY_LEVEL);
            return DEFAULT_QUERY_LEVEL;
        }
        
        int maxLevel = 0;
        boolean foundDailiRole = false;
        
        for (String roleCode : roleCodes) {
            if (StrUtil.isBlank(roleCode)) {
                continue;
            }
            
            // 匹配代理角色编码
            Matcher matcher = DAILI_PATTERN.matcher(roleCode.trim());
            if (matcher.matches()) {
                foundDailiRole = true;
                try {
                    int level = Integer.parseInt(matcher.group(1));
                    if (level > maxLevel) {
                        maxLevel = level;
                        log.debug("找到代理角色：{}，层数：{}", roleCode, level);
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析代理角色层数失败，角色编码：{}，错误：{}", roleCode, e.getMessage());
                }
            }
        }
        
        // 如果没有找到代理角色，使用默认层数
        if (!foundDailiRole) {
            log.debug("未找到代理角色，使用默认查询层数：{}", DEFAULT_QUERY_LEVEL);
            return DEFAULT_QUERY_LEVEL;
        }
        
        // 验证层数范围
        if (maxLevel < MIN_QUERY_LEVEL) {
            log.warn("解析出的查询层数小于最小值，层数：{}，使用最小值：{}", maxLevel, MIN_QUERY_LEVEL);
            return MIN_QUERY_LEVEL;
        }

        log.warn("解析出的查询层数超过最大值，层数：{}，使用最大值：{}", maxLevel, MAX_QUERY_LEVEL);
        return MAX_QUERY_LEVEL;

    }
    
    /**
     * 验证查询层数是否在有效范围内
     * 
     * @param level 查询层数
     * @return 验证后的查询层数
     */
    public static int validateQueryLevel(Integer level) {
        if (level == null) {
            return DEFAULT_QUERY_LEVEL;
        }
        
        if (level < MIN_QUERY_LEVEL) {
            return MIN_QUERY_LEVEL;
        }

        return MAX_QUERY_LEVEL;

    }
    
    /**
     * 获取默认查询层数
     * 
     * @return 默认查询层数
     */
    public static int getDefaultQueryLevel() {
        return DEFAULT_QUERY_LEVEL;
    }
    
    /**
     * 获取最大查询层数
     * 
     * @return 最大查询层数
     */
    public static int getMaxQueryLevel() {
        return MAX_QUERY_LEVEL;
    }
    
    /**
     * 获取最小查询层数
     * 
     * @return 最小查询层数
     */
    public static int getMinQueryLevel() {
        return MIN_QUERY_LEVEL;
    }
}
