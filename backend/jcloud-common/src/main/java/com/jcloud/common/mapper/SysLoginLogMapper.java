package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysLoginLog;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录日志Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysLoginLogMapper extends BaseMapper<SysLoginLog> {
    
    /**
     * 根据用户名查询登录日志
     * 
     * @param userName 用户名
     * @param tenantId 租户ID
     * @return 登录日志列表
     */
    @Select("SELECT * FROM sys_login_log WHERE user_name = #{userName} AND tenant_id = #{tenantId} " +
            "ORDER BY login_time DESC LIMIT 100")
    List<SysLoginLog> selectByUserName(@Param("userName") String userName, @Param("tenantId") Long tenantId);
    
    /**
     * 根据IP地址查询登录日志
     * 
     * @param ipaddr IP地址
     * @param tenantId 租户ID
     * @return 登录日志列表
     */
    @Select("SELECT * FROM sys_login_log WHERE ipaddr = #{ipaddr} AND tenant_id = #{tenantId} " +
            "ORDER BY login_time DESC")
    List<SysLoginLog> selectByIpaddr(@Param("ipaddr") String ipaddr, @Param("tenantId") Long tenantId);
    
    /**
     * 根据登录状态查询登录日志
     * 
     * @param status 登录状态
     * @param tenantId 租户ID
     * @return 登录日志列表
     */
    @Select("SELECT * FROM sys_login_log WHERE status = #{status} AND tenant_id = #{tenantId} " +
            "ORDER BY login_time DESC")
    List<SysLoginLog> selectByStatus(@Param("status") Integer status, @Param("tenantId") Long tenantId);
    
    /**
     * 统计指定时间范围内的登录日志数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 日志数量
     */
    @Select("SELECT COUNT(1) FROM sys_login_log " +
            "WHERE login_time >= #{startTime} AND login_time <= #{endTime} AND tenant_id = #{tenantId}")
    int countByTimeRange(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime, 
                        @Param("tenantId") Long tenantId);
    
    /**
     * 统计登录成功和失败的数量
     * 
     * @param tenantId 租户ID
     * @return 统计结果
     */
    @Select("SELECT status, COUNT(1) as count FROM sys_login_log " +
            "WHERE tenant_id = #{tenantId} GROUP BY status")
    @MapKey("status")
    List<java.util.Map<String, Object>> countByStatus(@Param("tenantId") Long tenantId);
    
    /**
     * 清理指定天数之前的登录日志
     * 
     * @param days 保留天数
     * @param tenantId 租户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM sys_login_log WHERE login_time < DATE_SUB(NOW(), INTERVAL #{days} DAY) AND tenant_id = #{tenantId}")
    int cleanOldLogs(@Param("days") int days, @Param("tenantId") Long tenantId);
    
    /**
     * 获取最近的登录日志
     * 
     * @param limit 限制数量
     * @param tenantId 租户ID
     * @return 登录日志列表
     */
    @Select("SELECT * FROM sys_login_log WHERE tenant_id = #{tenantId} " +
            "ORDER BY login_time DESC LIMIT #{limit}")
    List<SysLoginLog> selectRecentLogs(@Param("limit") int limit, @Param("tenantId") Long tenantId);
    
    /**
     * 统计今日登录数量
     * 
     * @param tenantId 租户ID
     * @return 登录数量
     */
    @Select("SELECT COUNT(1) FROM sys_login_log " +
            "WHERE DATE(login_time) = CURDATE() AND tenant_id = #{tenantId}")
    int countTodayLogins(@Param("tenantId") Long tenantId);
    
    /**
     * 统计登录失败数量
     * 
     * @param tenantId 租户ID
     * @return 登录失败数量
     */
    @Select("SELECT COUNT(1) FROM sys_login_log " +
            "WHERE status = 1 AND tenant_id = #{tenantId}")
    int countFailedLogins(@Param("tenantId") Long tenantId);
    
    /**
     * 获取用户最后登录信息
     * 
     * @param userName 用户名
     * @param tenantId 租户ID
     * @return 最后登录信息
     */
    @Select("SELECT * FROM sys_login_log WHERE user_name = #{userName} AND tenant_id = #{tenantId} " +
            "AND status = 0 ORDER BY login_time DESC LIMIT 1")
    SysLoginLog selectLastLoginByUserName(@Param("userName") String userName, @Param("tenantId") Long tenantId);
}
